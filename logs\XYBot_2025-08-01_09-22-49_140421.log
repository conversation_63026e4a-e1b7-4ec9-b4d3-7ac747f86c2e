2025-08-01 09:22:50 | SUCCESS | 读取主设置成功
2025-08-01 09:22:50 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 GetRedisAddr: 127.0.0.1:6379
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 Server start at :9000
2025-08-01 09:22:50 | SUCCESS | WechatAPI服务已启动
2025-08-01 09:22:51 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 09:22:51 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 09:22:51 | SUCCESS | 登录成功
2025-08-01 09:22:51 | SUCCESS | 已开启自动心跳
2025-08-01 09:22:51 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:22:51 | SUCCESS | 数据库初始化成功
2025-08-01 09:22:51 | SUCCESS | 定时任务已启动
2025-08-01 09:22:51 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 09:22:51 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:52 | INFO | 播客API初始化成功
2025-08-01 09:22:52 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:22:52 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 09:22:52 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 09:22:52 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 09:22:52 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 09:22:52 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 09:22:52 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 09:22:52 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 09:22:52 | DEBUG |   - 启用状态: True
2025-08-01 09:22:52 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 09:22:52 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 09:22:52 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 09:22:52 | DEBUG |   - Cookies配置: 已配置
2025-08-01 09:22:52 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 09:22:52 | DEBUG |   - 自然化响应: True
2025-08-01 09:22:52 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 09:22:52 | ERROR | 加载插件时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 51, in load_plugin
    plugin = plugin_class()
             ^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\MiniProgramTester\main.py", line 24, in __init__
    self._init_natural_responses()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MiniProgramTester' object has no attribute '_init_natural_responses'

2025-08-01 09:22:52 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 09:22:52 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 09:22:52 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 09:22:52 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 09:22:52 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 09:22:52 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 09:22:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:52 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 09:22:52 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 09:22:52 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 09:22:52 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 09:22:52 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 09:22:52 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 09:22:52 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 09:22:53 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 09:22:53 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 09:22:53 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 09:22:53 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 09:22:54 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 09:22:54 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:54 | INFO | [yuanbao] 插件初始化完成
2025-08-01 09:22:54 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 09:22:54 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 09:22:54 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 09:22:54 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 09:22:54 | INFO | 处理堆积消息中
2025-08-01 09:22:54 | SUCCESS | 处理堆积消息完毕
2025-08-01 09:22:54 | SUCCESS | 开始处理消息
2025-08-01 09:22:55 | DEBUG | 收到消息: {'MsgId': 1800315910, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="c7e27e1d454f532eddb1060c13db05cf" encryver="1" cdnthumbaeskey="c7e27e1d454f532eddb1060c13db05cf" cdnthumburl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" cdnthumblength="4880" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" length="95033" md5="8274777b21108b2c6fe61380899dfbfe" originsourcemd5="5b7fd7c2a948698bc376d31de554bff7">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011380, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>36d60c1e2464b50ae17d0bc01ebfa4d7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ZLPzhQR8|v1_zC0G+nrO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1175481510760757500, 'MsgSeq': 871416284}
2025-08-01 09:22:55 | INFO | 收到图片消息: 消息ID:1800315910 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="c7e27e1d454f532eddb1060c13db05cf" encryver="1" cdnthumbaeskey="c7e27e1d454f532eddb1060c13db05cf" cdnthumburl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" cdnthumblength="4880" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" length="95033" md5="8274777b21108b2c6fe61380899dfbfe" originsourcemd5="5b7fd7c2a948698bc376d31de554bff7"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:22:56 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 09:22:56 | INFO | [TimerTask] 缓存图片消息: 1800315910
2025-08-01 09:23:05 | DEBUG | 收到消息: {'MsgId': 1496175978, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@\xa0执傲\u2005给你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011390, 'MsgSource': '<msgsource>\n\t<atuserlist>qq631390473</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_F2wFMXYj|v1_8vtuIIhf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2889355027434464434, 'MsgSeq': 871416285}
2025-08-01 09:23:05 | INFO | 收到文本消息: 消息ID:1496175978 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['qq631390473'] 内容:@ 执傲 给你
2025-08-01 09:23:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ 执傲 给你' from wxid_5kipwrzramxr22 in 27852221909@chatroom
2025-08-01 09:23:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['@\xa0执傲\u2005给你']
2025-08-01 09:23:05 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:23:05 | DEBUG | 处理消息内容: '@ 执傲 给你'
2025-08-01 09:23:05 | DEBUG | 消息内容 '@ 执傲 给你' 不匹配任何命令，忽略
2025-08-01 09:23:10 | DEBUG | 收到消息: {'MsgId': 2047340988, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7c9d966e393ad4687c584073acbc3a47" encryver="1" cdnthumbaeskey="7c9d966e393ad4687c584073acbc3a47" cdnthumburl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" cdnthumblength="2742" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" length="61305" cdnbigimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" hdlength="2789224" md5="0bf26f1ea12444ef4415d26b527f61e2" hevc_mid_size="61305" originsourcemd5="0bf26f1ea12444ef4415d26b527f61e2">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjU1MDA1MDAwNTE0MDQxNTAiLCJwZHFoYXNoIjoiMzY3NTJhZTkzZDczZTk2MmQzZDJlNzczODk4ODQ0NzEyOGM5MTljODY3NjNhMjllZDY3N2UzYzIyMjYzNWUyNSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011395, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>e9cbc212e94d1e8d6ef2c2f9526388bb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_6dGd4kY4|v1_rLKBHYfY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2468378362155633300, 'MsgSeq': 871416286}
2025-08-01 09:23:10 | INFO | 收到图片消息: 消息ID:2047340988 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 XML:<?xml version="1.0"?><msg><img aeskey="7c9d966e393ad4687c584073acbc3a47" encryver="1" cdnthumbaeskey="7c9d966e393ad4687c584073acbc3a47" cdnthumburl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" cdnthumblength="2742" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" length="61305" cdnbigimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" hdlength="2789224" md5="0bf26f1ea12444ef4415d26b527f61e2" hevc_mid_size="61305" originsourcemd5="0bf26f1ea12444ef4415d26b527f61e2"><secHashInfoBase64>eyJwaGFzaCI6IjU1MDA1MDAwNTE0MDQxNTAiLCJwZHFoYXNoIjoiMzY3NTJhZTkzZDczZTk2MmQzZDJlNzczODk4ODQ0NzEyOGM5MTljODY3NjNhMjllZDY3N2UzYzIyMjYzNWUyNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:23:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:23:10 | INFO | [TimerTask] 缓存图片消息: 2047340988
2025-08-01 09:23:14 | DEBUG | 收到消息: {'MsgId': 430328861, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n基金我就挣了一天钱，现在天天亏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011399, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_qeCFNsiZ|v1_KvJxH59l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 基金我就挣了一天钱，现在天天亏', 'NewMsgId': 8497724168757424674, 'MsgSeq': 871416287}
2025-08-01 09:23:14 | INFO | 收到文本消息: 消息ID:430328861 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:基金我就挣了一天钱，现在天天亏
2025-08-01 09:23:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '基金我就挣了一天钱，现在天天亏' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:23:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['基金我就挣了一天钱，现在天天亏']
2025-08-01 09:23:15 | DEBUG | 处理消息内容: '基金我就挣了一天钱，现在天天亏'
2025-08-01 09:23:15 | DEBUG | 消息内容 '基金我就挣了一天钱，现在天天亏' 不匹配任何命令，忽略
2025-08-01 09:23:17 | DEBUG | 收到消息: {'MsgId': 646610057, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011402, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_IS4H13gx|v1_XRSyH+XE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : [捂脸]', 'NewMsgId': 2053202181805452766, 'MsgSeq': 871416288}
2025-08-01 09:23:17 | INFO | 收到表情消息: 消息ID:646610057 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:[捂脸]
2025-08-01 09:23:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2053202181805452766
