import os, json, time, random, uuid, hmac, hashlib, zlib, asyncio, base64
from datetime import datetime
from urllib.parse import urlencode, urlparse
from pathlib import Path
try: import tomllib
except: import tomli as tomllib
import httpx
from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message, on_emoji_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive
class DoubaoImageGenerator:
    def __init__(self, cookies):
        self.cookies = self._parse_cookies(cookies)
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*', 'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Accept-Encoding': 'gzip, deflate',
            'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via'
        }
        self.service_id = "a9rns2rl98"
        self.bot_id = "7338286299411103781"
        self.skill_id = "3"
        self.user_id = "3534286501452377"

    def _parse_cookies(self, cookie_string):
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        return cookies

    def _extract_user_id(self): return "3534286501452377"
    def _calculate_crc32(self, data): return zlib.crc32(data) & 0xffffffff

    async def get_upload_auth(self, client: httpx.AsyncClient):
        logger.debug(f"[DoubaoImageGenerator] 开始获取上传认证...")
        url = "https://www.doubao.com/alice/resource/prepare_upload"
        params = {'version_code': '20800', 'language': 'zh', 'device_platform': 'web', 'aid': '497858', 'real_aid': '497858', 'pkg_type': 'release_version', 'device_id': '7468716989062841895', 'web_id': '7468716986638386703', 'tea_uuid': '7468716986638386703', 'use-olympus-account': '1', 'region': 'CN', 'sys_region': 'CN', 'samantha_web': '1', 'pc_version': '2.24.2'}
        data = {"tenant_id": "5", "scene_id": "5", "resource_type": 2}

        logger.debug(f"[DoubaoImageGenerator] 请求上传认证 URL: {url}")
        logger.debug(f"[DoubaoImageGenerator] 请求参数: {params}")
        logger.debug(f"[DoubaoImageGenerator] 请求数据: {data}")

        response = await client.post(url, params=params, json=data, timeout=30)
        logger.debug(f"[DoubaoImageGenerator] 上传认证响应状态: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"[DoubaoImageGenerator] 获取上传认证失败，HTTP状态码: {response.status_code}")
            logger.error(f"[DoubaoImageGenerator] 响应内容: {response.text}")
            raise Exception(f"HTTP {response.status_code}")

        result = response.json()
        logger.debug(f"[DoubaoImageGenerator] 上传认证响应: {result}")

        if result.get('code') != 0:
            logger.error(f"[DoubaoImageGenerator] 获取上传认证失败，错误码: {result.get('code')}, 错误信息: {result.get('message', '未知错误')}")
            raise Exception("获取上传认证失败")

        upload_auth_token = result['data']['upload_auth_token']
        logger.info(f"[DoubaoImageGenerator] 成功获取上传认证，token长度: {len(str(upload_auth_token))}")
        return upload_auth_token

    async def upload_image(self, client: httpx.AsyncClient, image_path):
        try:
            logger.debug(f"[DoubaoImageGenerator] 开始上传图片: {image_path}")

            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()
            file_size, file_ext, crc32_value = len(image_data), os.path.splitext(image_path)[1], self._calculate_crc32(image_data)

            logger.info(f"[DoubaoImageGenerator] 图片信息 - 大小: {file_size/1024:.1f}KB, 扩展名: {file_ext}, CRC32: {crc32_value:08x}")

            # 获取上传认证
            upload_auth = await self.get_upload_auth(client)

            # 申请上传
            upload_params = {'Action': 'ApplyImageUpload', 'Version': '2018-08-01', 'ServiceId': self.service_id, 'NeedFallback': 'true', 'FileSize': str(file_size), 'FileExtension': file_ext, 's': 'yy49d6n7o6p'}
            apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"

            logger.debug(f"[DoubaoImageGenerator] 申请上传 URL: {apply_url}")

            headers = self.base_headers.copy()
            headers.update(self.generate_aws_signature('GET', apply_url, upload_auth))

            logger.debug(f"[DoubaoImageGenerator] 申请上传请求头: {headers}")

            response = await client.get(apply_url, headers=headers)
            logger.debug(f"[DoubaoImageGenerator] 申请上传响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"[DoubaoImageGenerator] 申请上传失败，状态码: {response.status_code}")
                logger.error(f"[DoubaoImageGenerator] 响应内容: {response.text}")
                return None

            response_json = response.json()
            if 'Result' not in response_json:
                logger.error(f"[DoubaoImageGenerator] 申请上传响应格式错误: {response_json}")
                return None

            result = response_json['Result']
            logger.debug(f"[DoubaoImageGenerator] 申请上传成功: {result}")

            # 执行上传
            store_info, upload_host = result['UploadAddress']['StoreInfos'][0], result['UploadAddress']['UploadHosts'][0]
            upload_url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"

            logger.info(f"[DoubaoImageGenerator] 开始上传到: {upload_url}")

            upload_headers = {
                'Authorization': store_info['Auth'],
                'Content-CRC32': f"{crc32_value:08x}",
                'Content-Type': 'application/octet-stream',
                'X-Storage-U': store_info['UploadID'],
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            logger.debug(f"[DoubaoImageGenerator] 上传请求头: {upload_headers}")

            response = await client.post(upload_url, content=image_data, headers=upload_headers)
            logger.debug(f"[DoubaoImageGenerator] 上传响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"[DoubaoImageGenerator] 上传失败，状态码: {response.status_code}")
                logger.error(f"[DoubaoImageGenerator] 响应内容: {response.text}")
                return None

            upload_response = response.json()
            logger.debug(f"[DoubaoImageGenerator] 上传响应: {upload_response}")

            if upload_response.get('code') != 2000:
                logger.error(f"[DoubaoImageGenerator] 上传失败，错误码: {upload_response.get('code')}")
                return None

            logger.info(f"[DoubaoImageGenerator] 图片上传成功")

            # 提交上传
            commit_params = {'Action': 'CommitImageUpload', 'Version': '2018-08-01', 'ServiceId': self.service_id}
            commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"
            commit_payload = json.dumps({"SessionKey": result['UploadAddress']['SessionKey']})

            logger.debug(f"[DoubaoImageGenerator] 提交上传 URL: {commit_url}")
            logger.debug(f"[DoubaoImageGenerator] 提交载荷: {commit_payload}")

            commit_headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            commit_headers.update(self.generate_aws_signature('POST', commit_url, upload_auth, commit_payload))

            logger.debug(f"[DoubaoImageGenerator] 提交请求头: {commit_headers}")

            response = await client.post(commit_url, content=commit_payload, headers=commit_headers)
            logger.debug(f"[DoubaoImageGenerator] 提交响应状态: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"[DoubaoImageGenerator] 提交上传失败，状态码: {response.status_code}")
                logger.error(f"[DoubaoImageGenerator] 响应内容: {response.text}")
                return None

            commit_response = response.json()
            logger.debug(f"[DoubaoImageGenerator] 提交响应: {commit_response}")

            if 'Result' not in commit_response:
                logger.error(f"[DoubaoImageGenerator] 提交响应格式错误")
                return None

            image_uri = commit_response['Result']['Results'][0]['Uri']
            logger.info(f"[DoubaoImageGenerator] 图片上传完成，URI: {image_uri}")
            return image_uri

        except Exception as e:
            logger.error(f"[DoubaoImageGenerator] 上传图片异常: {str(e)}")
            import traceback
            logger.error(f"[DoubaoImageGenerator] 异常详情: {traceback.format_exc()}")
            return None

    def generate_aws_signature(self, method, url, upload_auth, payload=""):
        access_key, secret_key, session_token = upload_auth['access_key'], upload_auth['secret_key'], upload_auth['session_token']
        parsed = urlparse(url)
        host, path, query = parsed.netloc, parsed.path or '/', parsed.query
        t = datetime.utcnow()
        amz_date, date_stamp = t.strftime('%Y%m%dT%H%M%SZ'), t.strftime('%Y%m%d')
        canonical_querystring = '&'.join([f'{k}={v}' for k, v in sorted([(param.split('=', 1) if '=' in param else (param, '')) for param in query.split('&')])]) if query else ''
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()
        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'
        def sign(key, msg): return hmac.new(key.encode('utf-8') if isinstance(key, str) else key, msg.encode('utf-8'), hashlib.sha256).digest()
        def get_signature_key(key, date_stamp, region_name, service_name): return sign(sign(sign(sign('AWS4' + key, date_stamp), region_name), service_name), 'aws4_request')
        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'
        return {'Authorization': authorization_header, 'X-Amz-Date': amz_date, 'x-amz-security-token': session_token}

    # 识图功能已移植到DoubaoImageRecognition插件
    # def recognize_image(self, image_uri, prompt="解释图片"):

    async def generate_image(self, client: httpx.AsyncClient, image_uri, prompt="更换背景", style_value=None):
        try:
            logger.info(f"[DoubaoImageGenerator] 开始生成图片 - 提示词: '{prompt}', 图片URI: {image_uri}, 风格: {style_value}")

            base_params = {'version_code': '20800', 'language': 'zh', 'device_platform': 'web', 'aid': '497858', 'real_aid': '497858', 'pkg_type': 'release_version', 'device_id': '7468716989062841895', 'web_id': '7468716986638386703', 'tea_uuid': '7468716986638386703', 'use-olympus-account': '1', 'region': 'CN', 'sys_region': 'CN', 'samantha_web': '1', 'pc_version': '2.24.2'}

            # 构建请求数据
            attachment_extra = {"refer_types": "overall"}
            if style_value:
                attachment_extra["style"] = style_value
                logger.debug(f"[DoubaoImageGenerator] 使用风格参数: {style_value}")

            request_data = {
                "messages": [{
                    "content": json.dumps({"text": prompt + "！"}),
                    "content_type": 2009,
                    "attachments": [{
                        "type": "image",
                        "key": image_uri,
                        "extra": attachment_extra,
                        "identifier": str(uuid.uuid4())
                    }],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",
                "local_conversation_id": f"local_{int(time.time() * 1000000)}",
                "local_message_id": str(uuid.uuid4())
            }

            logger.debug(f"[DoubaoImageGenerator] 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")

            headers = self.base_headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*',
                'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
                'Agw-Js-Conv': 'str',
                'last-event-id': 'undefined',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty'
            })

            logger.debug(f"[DoubaoImageGenerator] 请求头: {headers}")

            completion_url = f"https://www.doubao.com/samantha/chat/completion?{urlencode(base_params)}"
            logger.debug(f"[DoubaoImageGenerator] 请求URL: {completion_url}")

            async with client.stream('POST', completion_url, params=base_params, json=request_data, headers=headers, timeout=300) as response:
                logger.debug(f"[DoubaoImageGenerator] 流式响应状态: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"[DoubaoImageGenerator] 生成图片请求失败，状态码: {response.status_code}")
                    return [], None

                start_time, text_response = time.time(), ""
                line_count = 0
                text_image_pairs = []  # 用于收集文本和图片的配对
                current_text_segment = ""  # 当前文本段落

                logger.info(f"[DoubaoImageGenerator] 开始处理流式响应...")

                async for raw_line in response.aiter_lines():
                    line_count += 1
                    elapsed_time = time.time() - start_time

                    if elapsed_time > 180:
                        logger.warning(f"[DoubaoImageGenerator] 处理超时 (180秒)，已处理 {line_count} 行")
                        break

                    try:
                        line = raw_line
                    except:
                        line = str(raw_line, errors='replace')

                    if not line.strip() or not line.startswith('data: '):
                        continue

                    try:
                        data_str = line[6:].strip()
                        if not data_str or data_str == '[DONE]':
                            logger.debug(f"[DoubaoImageGenerator] 收到结束标记或空数据")
                            continue

                        event_data = json.loads(data_str)
                        logger.debug(f"[DoubaoImageGenerator] 第{line_count}行事件数据: {event_data}")

                        if 'event_data' in event_data and event_data.get('event_type') == 2001:
                            try:
                                inner_data = json.loads(event_data['event_data'])
                            except:
                                try:
                                    inner_data = json.loads(event_data['event_data'].replace('\\"', '"').replace('\\\\', '\\'))
                                except:
                                    logger.warning(f"[DoubaoImageGenerator] 解析内部数据失败: {event_data['event_data'][:100]}...")
                                    continue

                            logger.debug(f"[DoubaoImageGenerator] 内部数据: {inner_data}")

                            # 检查错误码
                            if inner_data.get('code', '').startswith('71002'):
                                logger.error(f"[DoubaoImageGenerator] 收到错误码: {inner_data.get('code')}")
                                return None, None

                            if 'message' in inner_data:
                                message = inner_data['message']
                                content_type = message.get('content_type')

                                logger.debug(f"[DoubaoImageGenerator] 消息类型: {content_type}")

                                # 处理图片生成结果 (content_type 2074)
                                if content_type == 2074 and 'content' in message:
                                    try:
                                        content = json.loads(message['content'])
                                        logger.debug(f"[DoubaoImageGenerator] 图片内容: {content}")
                                    except:
                                        logger.warning(f"[DoubaoImageGenerator] 解析图片内容失败")
                                        continue

                                    if 'creations' in content:
                                        logger.info(f"[DoubaoImageGenerator] 找到 {len(content['creations'])} 个创作结果")

                                        for i, creation in enumerate(content['creations']):
                                            logger.debug(f"[DoubaoImageGenerator] 创作结果 {i+1}: {creation}")

                                            if creation.get('type') == 1 and 'image' in creation:
                                                image_info = creation['image']
                                                status = image_info.get('status')

                                                logger.info(f"[DoubaoImageGenerator] 图片状态: {status}")

                                                if status == 2:  # 生成成功
                                                    urls = ['image_raw', 'image_ori', 'image_thumb_ori', 'preview_img', 'image_thumb']
                                                    for url_type in urls:
                                                        url_obj = image_info.get(url_type)
                                                        url = url_obj.get('url') if isinstance(url_obj, dict) else None
                                                        if url:
                                                            logger.info(f"[DoubaoImageGenerator] 图片生成成功，URL类型: {url_type}, URL: {url}")

                                                            # 创建文本-图片配对
                                                            pair = {
                                                                "text": current_text_segment.strip(),
                                                                "image_url": url
                                                            }
                                                            text_image_pairs.append(pair)
                                                            logger.info(f"[DoubaoImageGenerator] 创建配对 {len(text_image_pairs)}: 文本='{current_text_segment.strip()[:50]}...', 图片={url}")

                                                            # 重置当前文本段落
                                                            current_text_segment = ""
                                                            break  # 找到第一个有效URL就跳出

                                                elif status == 3:  # 生成失败
                                                    logger.error(f"[DoubaoImageGenerator] 图片生成失败，状态: {status}")

                                # 处理文本响应 (content_type 10000)
                                elif content_type == 10000:
                                    try:
                                        content = json.loads(message['content'])
                                        if 'text' in content and content['text']:
                                            text_response += content['text']
                                            current_text_segment += content['text']  # 累积当前文本段落
                                            logger.debug(f"[DoubaoImageGenerator] 累积文本响应: {text_response}")
                                            logger.debug(f"[DoubaoImageGenerator] 当前文本段落: {current_text_segment}")
                                    except:
                                        pass

                                    if 'tts_content' in inner_data and inner_data['tts_content']:
                                        tts_text = inner_data['tts_content'].strip()
                                        text_response = tts_text
                                        # 如果当前文本段落为空，使用TTS内容；否则累积
                                        if not current_text_segment.strip():
                                            current_text_segment = tts_text
                                        else:
                                            current_text_segment += tts_text
                                        logger.debug(f"[DoubaoImageGenerator] TTS文本响应: {tts_text}")
                                        logger.debug(f"[DoubaoImageGenerator] 当前文本段落: {current_text_segment}")

                    except Exception as e:
                        logger.warning(f"[DoubaoImageGenerator] 处理第{line_count}行时出错: {str(e)}")
                        continue

                # 处理剩余的文本段落（如果有的话）
                if current_text_segment.strip():
                    logger.info(f"[DoubaoImageGenerator] 处理剩余文本段落: '{current_text_segment.strip()}'")
                    # 检查是否已经有配对包含这个文本，避免重复
                    text_already_paired = False
                    for existing_pair in text_image_pairs:
                        if existing_pair['text'] == current_text_segment.strip():
                            text_already_paired = True
                            break

                    # 只有当文本没有被配对过时，才创建新的配对
                    if not text_already_paired:
                        pair = {
                            "text": current_text_segment.strip(),
                            "image_url": None
                        }
                        text_image_pairs.append(pair)
                        logger.info(f"[DoubaoImageGenerator] 创建剩余文本配对: '{current_text_segment.strip()[:50]}...'")
                    else:
                        logger.info(f"[DoubaoImageGenerator] 文本已配对，跳过重复创建")

                logger.info(f"[DoubaoImageGenerator] 流式响应处理完成，共处理 {line_count} 行，耗时 {elapsed_time:.1f}秒")
                logger.info(f"[DoubaoImageGenerator] 总共收集到 {len(text_image_pairs)} 个文本-图片配对")

                for i, pair in enumerate(text_image_pairs):
                    logger.info(f"[DoubaoImageGenerator] 配对 {i+1}: 文本='{pair['text'][:50]}...', 图片={'有' if pair['image_url'] else '无'}")

                if text_image_pairs:
                    return text_image_pairs, text_response.strip() if text_response.strip() else None
                else:
                    return [], text_response.strip() if text_response.strip() else None

        except Exception as e:
            logger.error(f"[DoubaoImageGenerator] 生成图片异常: {str(e)}")
            import traceback
            logger.error(f"[DoubaoImageGenerator] 异常详情: {traceback.format_exc()}")
            return [], None

    # 识图功能已移植到DoubaoImageRecognition插件
    # def process_image_recognition(self, image_path, prompt="解释图片"):
    #     image_uri = self.upload_image(image_path)
    #     return self.recognize_image(image_uri, prompt) if image_uri else None

    async def process_image(self, image_path, prompt="更换背景", style_value=None):
        logger.info(f"[DoubaoImageGenerator] 开始处理图片流程 - 路径: {image_path}, 提示词: '{prompt}', 风格: {style_value}")

        try:
            async with httpx.AsyncClient(cookies=self.cookies, timeout=300) as client:
                logger.debug(f"[DoubaoImageGenerator] 创建HTTP客户端成功，超时设置: 300秒")

                # 步骤1: 上传图片
                logger.info(f"[DoubaoImageGenerator] 步骤1: 上传图片...")
                image_uri = await self.upload_image(client, image_path)

                if not image_uri:
                    logger.error(f"[DoubaoImageGenerator] 图片上传失败")
                    return (None, None)

                logger.info(f"[DoubaoImageGenerator] 图片上传成功，URI: {image_uri}")

                # 步骤2: 生成图片
                logger.info(f"[DoubaoImageGenerator] 步骤2: 生成图片...")
                result = await self.generate_image(client, image_uri, prompt, style_value)

                if result[0] and len(result[0]) > 0:
                    logger.info(f"[DoubaoImageGenerator] 图片处理流程完成，生成了 {len(result[0])} 个文本-图片配对")
                    for i, pair in enumerate(result[0]):
                        logger.info(f"[DoubaoImageGenerator] 配对 {i+1}: 文本='{pair['text'][:50]}...', 图片={'有' if pair['image_url'] else '无'}")
                else:
                    logger.warning(f"[DoubaoImageGenerator] 图片生成失败")

                return result

        except Exception as e:
            logger.error(f"[DoubaoImageGenerator] 处理图片流程异常: {str(e)}")
            import traceback
            logger.error(f"[DoubaoImageGenerator] 异常详情: {traceback.format_exc()}")
            return ([], None)


class DouBaoImageToImage(PluginBase):
    description = "豆包AI图生图功能，支持图像编辑、换装、风格转换等"
    author = "XYBot"
    version = "1.1.0"
    plugin_name = "DouBaoImageToImage"

    def __init__(self):
        super().__init__()
        logger.info(f"[{self.plugin_name}] ========== 初始化豆包图生图插件 ==========")

        # 创建临时目录
        self.temp_dir = Path("temp/doubao_image_to_image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f"[{self.plugin_name}] 临时目录创建: {self.temp_dir}")

        # 加载配置
        logger.debug(f"[{self.plugin_name}] 开始加载配置...")
        self._load_config()

        # 初始化状态变量
        self.user_last_request, self.user_request_count, self.user_sessions, self.emoji_message_cache = {}, {}, {}, {}
        self.quote_command = ["豆包"]

        # 支持的比例配置
        self.aspect_ratios = {
            "1:1": {"width": 1024, "height": 1024, "resolution_type": "1k", "ratio_text": "1:1"},
            "2:3": {"width": 832, "height": 1248, "resolution_type": "1k", "ratio_text": "2:3"},
            "4:3": {"width": 1248, "height": 936, "resolution_type": "1k", "ratio_text": "4:3"},
            "9:16": {"width": 936, "height": 1664, "resolution_type": "1k", "ratio_text": "9:16"},
            "16:9": {"width": 1664, "height": 936, "resolution_type": "1k", "ratio_text": "16:9"}
        }

        # 支持的图片风格
        self.image_styles = {
            "人像摄影": "skill_image_styles_portrait", "电影写真": "skill_image_styles_film",
            "中国风": "skill_image_styles_chinese", "动漫": "skill_image_styles_japanese_anime",
            "3D渲染": "skill_image_styles_3d", "赛博朋克": "image_gen_style_cyberpunk",
            "CG动画": "skill_image_styles_cg", "水墨画": "skill_image_styles_ink_wash_painting",
            "油画": "skill_image_styles_oil_painting", "古典": "skill_image_styles_classic",
            "水彩画": "skill_image_styles_watercolor", "卡通": "skill_image_styles_cartoon",
            "平面插画": "skill_image_styles_flat_illustration", "风景": "skill_image_styles_landscape",
            "港风动漫": "skill_image_styles_hongkong_anime", "像素风格": "skill_image_styles_pixel_style",
            "荧光绘画": "skill_image_styles_fluorescence", "彩铅画": "skill_image_styles_colored_pencil",
            "手办": "skill_image_styles_figure", "儿童绘画": "skill_image_styles_children_illustration",
            "抽象": "skill_image_styles_abstract", "锐笔插画": "skill_image_styles_sharp_illustration",
            "二次元": "skill_image_styles_acg", "油墨印刷": "skill_image_styles_ink_print",
            "版画": "skill_image_styles_bnw_printing", "莫奈": "skill_image_styles_monet",
            "毕加索": "skill_image_styles_picasso", "伦勃朗": "skill_image_styles_rembrandt",
            "马蒂斯": "skill_image_styles_matisse", "巴洛克": "skill_image_styles_baroque",
            "复古动漫": "skill_image_styles_oldschool", "绘本": "skill_image_styles_picturebook"
        }

        logger.info(f"[{self.plugin_name}] 插件初始化完成")
        logger.info(f"[{self.plugin_name}] 支持 {len(self.aspect_ratios)} 种比例，{len(self.image_styles)} 种风格")
        logger.info(f"[{self.plugin_name}] 插件状态: {'启用' if self.enable else '禁用'}")
        logger.info(f"[{self.plugin_name}] 冷却时间: {self.cooldown}秒")
        logger.info(f"[{self.plugin_name}] ========== 插件初始化完成 ==========")

    def _load_config(self):
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            with open(config_path, "rb") as f: config = tomllib.load(f).get(self.plugin_name, {})
        except: config = {}
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["豆包图生图"])
        self.command_format = config.get("command-format", "豆包图生图 [提示词] [图片路径]")
        # 识图功能已移植到DoubaoImageRecognition插件
        # recognition_config = config.get("recognition", {})
        # self.recognition_command = recognition_config.get("command", ["豆包识图", "识图"])
        # self.recognition_command_format = recognition_config.get("command-format", "豆包识图 [提示词] [图片路径] 或 引用图片并发送: 识图 [提示词]")
        quote_config = config.get("quote", {})
        self.quote_command = quote_config.get("command", ["豆包"])
        self.quote_command_format = quote_config.get("command-format", "引用图片并发送: 豆包 [提示词]")
        self.api_config = config.get("api", {})
        self.base_url = self.api_config.get("base_url", "https://www.doubao.com")
        self.cookies = self.api_config.get("api_key", "")
        self.model = self.api_config.get("model", "doubao-image-generation")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 15)
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    def _parse_prompt_style_and_ratio(self, prompt_text):
        selected_ratio, selected_style = self.aspect_ratios["2:3"], None
        words = prompt_text.strip().split()
        remaining_words = []
        for word in words:
            if word in self.aspect_ratios: selected_ratio = self.aspect_ratios[word]; continue
            style_found = False
            for style_name, style_value in self.image_styles.items():
                if style_name in word or word in style_name: selected_style = style_value; style_found = True; break
            if not style_found: remaining_words.append(word)
        clean_prompt = " ".join(remaining_words)
        ratio_text = selected_ratio["ratio_text"]
        if selected_style:
            style_name = next((name for name, value in self.image_styles.items() if value == selected_style), None)
            full_prompt = f"图片风格为「{style_name}」，比例「{ratio_text}」{clean_prompt}" if style_name else f"{clean_prompt}，比例「{ratio_text}」"
        else: full_prompt = f"{clean_prompt}，比例「{ratio_text}」"
        return full_prompt, selected_ratio, selected_style

    def _parse_prompt_and_ratio(self, prompt_text):
        full_prompt, ratio_config, _ = self._parse_prompt_style_and_ratio(prompt_text)
        return full_prompt, ratio_config

    async def _simple_confirm(self, bot, wxid, custom_message=None):
        if custom_message: await bot.send_text_message(wxid, custom_message)
        elif self.natural_response: await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            logger.debug(f"[{self.plugin_name}] 插件已禁用，跳过处理")
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        logger.debug(f"[{self.plugin_name}] 收到文本消息: '{content}' from {user_wxid} in {wxid}")

        # 检查是否是帮助命令
        if content in ["豆包图生图帮助", "豆包图生图说明", "豆包图生图指令"]:
            logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 请求帮助信息")
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        # 检查是否是插件命令
        command_parts = content.split(" ", 2)  # 分割成三部分：命令、提示词、图片路径
        logger.debug(f"[{self.plugin_name}] 命令解析: {command_parts}")

        # 标准图生图命令处理
        if command_parts[0] in self.command:
            logger.info(f"[{self.plugin_name}] 识别到图生图命令: {command_parts[0]}")
            # 解析命令格式: 豆包图生图 [提示词] [图片路径]
            if len(command_parts) < 3:
                logger.warning(f"[{self.plugin_name}] 命令格式错误，参数不足: {len(command_parts)}")
                await bot.send_at_message(
                    wxid,
                    "❌ 命令格式错误\n正确格式: 豆包图生图 [提示词] [图片路径]",
                    [user_wxid]
                )
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            prompt = command_parts[1].strip()
            image_path = command_parts[2].strip()

            logger.info(f"[{self.plugin_name}] 解析命令参数 - 提示词: '{prompt}', 图片路径: '{image_path}'")

            # 检查限流
            logger.debug(f"[{self.plugin_name}] 开始检查用户限流...")
            if self._check_rate_limit(user_wxid):
                logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 被限流，拒绝请求")
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    logger.debug(f"[{self.plugin_name}] 发送自然语言限流消息: '{rate_limit_msg}'")
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            # 检查图片是否存在
            logger.debug(f"[{self.plugin_name}] 检查图片文件是否存在: {image_path}")
            if not os.path.exists(image_path):
                logger.error(f"[{self.plugin_name}] 图片文件不存在: {image_path}")
                await bot.send_at_message(
                    wxid,
                    f"❌ 图片不存在: {image_path}\n请确认图片路径是否正确",
                    [user_wxid]
                )
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            logger.info(f"[{self.plugin_name}] 图片文件验证通过: {image_path}")

            try:
                # 解析提示词、风格和比例
                logger.debug(f"[{self.plugin_name}] 开始解析提示词、风格和比例...")
                parsed_prompt, ratio_config, style_value = self._parse_prompt_style_and_ratio(prompt)

                logger.info(f"[{self.plugin_name}] 提示词解析完成:")
                logger.info(f"[{self.plugin_name}]   - 原始提示词: '{prompt}'")
                logger.info(f"[{self.plugin_name}]   - 解析后提示词: '{parsed_prompt}'")
                logger.info(f"[{self.plugin_name}]   - 比例配置: {ratio_config['ratio_text']} ({ratio_config['width']}x{ratio_config['height']})")
                logger.info(f"[{self.plugin_name}]   - 风格值: {style_value}")

                # 执行图生图流程
                logger.info(f"[{self.plugin_name}] 开始执行图生图流程...")
                await self._image_generation_flow(bot, wxid, user_wxid, parsed_prompt, image_path, ratio_config, style_value)

            except Exception as e:
                logger.error(f"[{self.plugin_name}] ❌ 处理失败: {str(e)}")
                import traceback
                logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
                await bot.send_at_message(
                    wxid,
                    f"❌ 处理失败: {str(e)}",
                    [user_wxid]
                )
            return
        # 识图功能已移植到DoubaoImageRecognition插件
        # elif command_parts[0] in self.recognition_command:
        #     # 识图命令处理已移除
        else:
            # 不是有效的命令
            pass

    @on_emoji_message
    async def handle_emoji(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        try:
            msg_id = message.get('NewMsgId') or message.get('MsgId')
            if not msg_id:
                return

            # 提取表情信息
            emoji_info = {
                'EmojiUrl': message.get('EmojiUrl'),
                'EmojiMD5': message.get('EmojiMD5'),
                'EmojiLen': message.get('EmojiLen'),
                'Content': message.get('Content', ''),
                'CreateTime': message.get('CreateTime'),
                'SenderWxid': message.get('SenderWxid')
            }

            # 从Content中提取aeskey
            content = message.get('Content', '')
            if 'aeskey=' in content:
                try:
                    aeskey_start = content.find('aeskey="') + 8
                    aeskey_end = content.find('"', aeskey_start)
                    if aeskey_start > 7 and aeskey_end > aeskey_start:
                        emoji_info['aeskey'] = content[aeskey_start:aeskey_end]
                except Exception:
                    pass

            # 缓存表情信息
            self.emoji_message_cache[str(msg_id)] = emoji_info



            # 限制缓存大小，避免内存泄漏
            if len(self.emoji_message_cache) > 1000:
                # 删除最旧的100个缓存项
                oldest_keys = list(self.emoji_message_cache.keys())[:100]
                for key in oldest_keys:
                    del self.emoji_message_cache[key]

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情消息缓存时出错: {str(e)}")

    def _process_emoji_to_image(self, emoji_path):
        """将表情包转换为可识图的图片

        Args:
            emoji_path: 下载的表情文件路径(.bin格式)

        Returns:
            转换后的图片路径，失败返回None
        """
        try:
            # 将.bin文件重命名为.gif
            gif_path = emoji_path.replace('.bin', '.gif')
            if emoji_path != gif_path:
                os.rename(emoji_path, gif_path)
            else:
                gif_path = emoji_path



            # 提取第一帧并转换为jpg
            try:
                from PIL import Image
                with Image.open(gif_path) as img:
                    # 获取第一帧
                    img.seek(0)
                    # 转换为RGB模式（去除透明通道）
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 保存为jpg格式
                    jpg_path = gif_path.replace('.gif', '_frame.jpg')
                    img.save(jpg_path, 'JPEG', quality=95)


                    return jpg_path

            except ImportError:
                logger.error(f"[{self.plugin_name}] 缺少PIL库，无法处理表情包")
                return None
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 提取表情帧失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情文件失败: {str(e)}")
            return None

    async def _download_emoji(self, bot, emoji_info):
        """下载表情文件

        Args:
            emoji_info: 表情信息字典

        Returns:
            下载的文件路径，失败返回None
        """
        try:
            emoji_url = emoji_info.get('EmojiUrl')
            emoji_md5 = emoji_info.get('EmojiMD5')
            aeskey = emoji_info.get('aeskey')

            if not emoji_url or not emoji_md5:
                logger.error(f"[{self.plugin_name}] 表情信息不完整: url={bool(emoji_url)}, md5={bool(emoji_md5)}")
                return None



            # 使用微信API下载表情
            if aeskey:
                # 如果有aeskey，使用加密下载
                emoji_base64 = await bot.download_image(aeskey, emoji_url)
            else:
                # 直接下载
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        response = await client.get(emoji_url)
                        if response.status_code == 200:
                            emoji_base64 = base64.b64encode(response.content).decode()
                        else:
                            logger.error(f"[{self.plugin_name}] 下载表情失败: HTTP {response.status_code}")
                            return None
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 直接下载表情失败: {str(e)}")
                    return None

            if not emoji_base64:
                logger.error(f"[{self.plugin_name}] 表情下载失败")
                return None

            # 保存表情文件
            temp_file = self.temp_dir / f"emoji_{emoji_md5}_{int(time.time())}.bin"
            emoji_data = base64.b64decode(emoji_base64)
            with open(temp_file, "wb") as f:
                f.write(emoji_data)


            return str(temp_file)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载表情时出错: {str(e)}")
            return None

    def _cleanup_temp_files(self, file_paths: list, delay_seconds: int = 0):
        """批量清理临时文件 - 使用统一管理器

        Args:
            file_paths: 要删除的文件路径列表
            delay_seconds: 延迟删除时间（秒），0表示立即删除
        """
        for file_path in file_paths:
            if file_path and isinstance(file_path, str):
                cleanup_file(file_path, delay_seconds)

    async def _handle_emoji_quote(self, bot: WechatAPIClient, wxid: str, user_wxid: str,
                                 quoted_msg_id: str, prompt: str,
                                 ratio_config: dict = None, style_value: str = None):
        """处理表情消息引用（仅图生图模式）

        Args:
            bot: 微信API客户端
            wxid: 聊天ID
            user_wxid: 用户微信ID
            quoted_msg_id: 被引用的消息ID
            prompt: 提示词
            ratio_config: 比例配置
            style_value: 风格值
        """
        try:


            # 从缓存中获取表情信息
            emoji_info = self.emoji_message_cache.get(quoted_msg_id)
            if not emoji_info:
                logger.warning(f"[{self.plugin_name}] 未找到表情消息缓存: {quoted_msg_id}")
                await bot.send_at_message(
                    wxid,
                    "❌ 表情消息信息已过期，请重新发送表情后再引用",
                    [user_wxid]
                )
                return



            # 下载表情文件
            emoji_file = await self._download_emoji(bot, emoji_info)
            if not emoji_file:
                await bot.send_at_message(
                    wxid,
                    "❌ 表情下载失败",
                    [user_wxid]
                )
                return

            # 转换表情为可识图的图片
            image_file = self._process_emoji_to_image(emoji_file)
            if not image_file:
                await bot.send_at_message(
                    wxid,
                    "❌ 表情处理失败，无法提取图片帧",
                    [user_wxid]
                )
                return



            # 识图功能已移植到DoubaoImageRecognition插件，只处理图生图
            # if is_recognition_mode:
            #     await self._recognition_flow_without_notification(bot, wxid, user_wxid, prompt, image_file)
            # else:
            await self._image_generation_flow_without_notification(bot, wxid, user_wxid, prompt, image_file, ratio_config, style_value)

            # 处理完成后清理表情相关的临时文件（延迟15秒）
            files_to_cleanup = [emoji_file]
            if image_file != emoji_file:
                files_to_cleanup.append(image_file)
            self._cleanup_temp_files(files_to_cleanup, delay_seconds=15)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情引用时出错: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理表情时出现错误: {str(e)}",
                [user_wxid]
            )

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息（仅图生图功能）"""
        if not self.enable:
            logger.debug(f"[{self.plugin_name}] 插件已禁用，跳过引用消息处理")
            return

        try:
            content = message.get("Content", "").strip()
            wxid = message["FromWxid"]
            user_wxid = message["SenderWxid"]

            logger.info(f"[{self.plugin_name}] ========== 收到引用消息 ==========")
            logger.info(f"[{self.plugin_name}] 消息内容: '{content}' from {user_wxid} in {wxid}")

            # 检查是否是图生图命令
            command_parts = content.split(" ", 2)
            logger.debug(f"[{self.plugin_name}] 引用命令解析: {command_parts}")

            if not command_parts or command_parts[0] not in self.quote_command:
                # 不是图生图命令，不处理
                logger.debug(f"[{self.plugin_name}] 不是图生图引用命令，跳过处理")
                return

            logger.info(f"[{self.plugin_name}] 识别到图生图引用命令: {command_parts[0]}")

            # 解析提示词
            if len(command_parts) == 1:
                # 只有命令，使用默认提示词
                prompt = "风格转换"
                logger.debug(f"[{self.plugin_name}] 使用默认提示词: '{prompt}'")
            else:
                # 有提示词
                prompt = " ".join(command_parts[1:]).strip()
                logger.debug(f"[{self.plugin_name}] 解析到提示词: '{prompt}'")

            # 解析提示词、风格和比例
            logger.debug(f"[{self.plugin_name}] 开始解析提示词、风格和比例...")
            parsed_prompt, ratio_config, style_value = self._parse_prompt_style_and_ratio(prompt)

            logger.info(f"[{self.plugin_name}] 引用图片图生图参数:")
            logger.info(f"[{self.plugin_name}]   - 原始提示词: '{prompt}'")
            logger.info(f"[{self.plugin_name}]   - 解析后提示词: '{parsed_prompt}'")
            logger.info(f"[{self.plugin_name}]   - 比例: {ratio_config['width']}x{ratio_config['height']} ({ratio_config['ratio_text']})")
            logger.info(f"[{self.plugin_name}]   - 风格: {style_value}")

            # 检查限流
            logger.debug(f"[{self.plugin_name}] 开始检查用户限流...")
            if self._check_rate_limit(user_wxid):
                logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 被限流，拒绝引用请求")
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    logger.debug(f"[{self.plugin_name}] 发送自然语言限流消息: '{rate_limit_msg}'")
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            # 处理引用的图片
            logger.info(f"[{self.plugin_name}] 开始处理引用的图片...")
            await self._handle_quoted_image(bot, wxid, user_wxid, message, parsed_prompt, ratio_config, style_value)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用消息失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理引用消息失败: {str(e)}",
                [user_wxid]
            )

    async def _handle_quoted_image(self, bot: WechatAPIClient, wxid: str, user_wxid: str, message: dict, prompt: str, ratio_config: dict, style_value: str):
        """处理引用的图片（仅图生图功能）"""
        try:
            # 获取引用的消息信息
            quote_info = message.get("Quote", {})
            quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")

            if not quoted_msg_id:
                await bot.send_at_message(wxid, "❌ 未找到引用的消息", [user_wxid])
                return

            # 检查被引用消息的类型 (3表示图片消息，47表示表情消息)
            quoted_msg_type = quote_info.get("MsgType")
            logger.info(f"[{self.plugin_name}] 被引用消息类型: {quoted_msg_type}")

            if quoted_msg_type not in [3, 47]:  # 支持图片和表情消息
                await bot.send_at_message(
                    wxid,
                    f"❌ 请引用图片或表情消息 (当前引用消息类型: {quoted_msg_type})",
                    [user_wxid]
                )
                return

            # 检查是否是机器人自己发的图片
            quoted_sender = quote_info.get("SenderWxid") or quote_info.get("FromWxid")
            bot_wxid = getattr(bot, 'wxid', None)

            if quoted_sender == bot_wxid:
                # 尝试从撤回插件获取缓存
                try:
                    from plugins.RevokePlugin.main import RevokePlugin
                    if RevokePlugin._instance:
                        msg_info = RevokePlugin._instance.get_message_by_id(quoted_msg_id)
                        if msg_info and 'local_image_path' in msg_info and os.path.exists(msg_info['local_image_path']):
                            await self._image_generation_flow_without_notification(
                                bot, wxid, user_wxid, prompt, msg_info['local_image_path'], ratio_config, style_value
                            )
                            return
                except:
                    pass

                await bot.send_at_message(
                    wxid,
                    "❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片",
                    [user_wxid]
                )
                return

            # 根据消息类型选择处理方式
            if quoted_msg_type == 47:
                # 处理表情消息引用
                await self._handle_emoji_quote(bot, wxid, user_wxid, quoted_msg_id, prompt, ratio_config, style_value)
                return

            # 处理图片消息引用 (MsgType 3)
            await self._handle_image_quote(bot, wxid, user_wxid, quote_info, prompt, ratio_config, style_value)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用图片失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理引用图片失败: {str(e)}",
                [user_wxid]
            )

    async def _handle_image_quote(self, bot: WechatAPIClient, wxid: str, user_wxid: str, quote_info: dict, prompt: str, ratio_config: dict, style_value: str):
        """处理图片消息引用"""
        try:
            # 从Quote.Content中提取XML
            quote_content = quote_info.get("Content", "")

            if not quote_content:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "❌ 无法获取引用的图片内容", [user_wxid])
                return

            # 解析XML内容
            import xml.etree.ElementTree as ET
            try:
                # 解析引用内容XML
                root = ET.fromstring(quote_content)

                # 首先尝试直接查找img节点
                img_node = root.find('.//img')

                # 如果没有直接的img节点，尝试查找refermsg节点中的content
                if img_node is None:
                    # 查找refermsg节点
                    refermsg = root.find('.//refermsg')
                    if refermsg is not None and refermsg.find('content') is not None:
                        # 提取refermsg中的content内容
                        content_text = refermsg.find('content').text
                        if content_text:
                            # content中的内容是XML格式，但被HTML编码，需要解码
                            content_text = content_text.replace('&lt;', '<').replace('&gt;', '>')
                            # 尝试解析内部的img标签
                            try:
                                inner_root = ET.fromstring(content_text)
                                img_node = inner_root.find('img')
                            except ET.ParseError:
                                logger.error(f"[{self.plugin_name}] 解析内部XML失败: {content_text[:100]}")

                # 如果仍然未找到img节点
                if img_node is None:
                    # 尝试直接从content中提取aeskey和cdnmidimgurl
                    if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                        aeskey_start = quote_content.find('aeskey="') + 8
                        aeskey_end = quote_content.find('"', aeskey_start)
                        aeskey = quote_content[aeskey_start:aeskey_end]

                        cdnmidimgurl_start = quote_content.find('cdnmidimgurl="') + 14
                        cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                        cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]

                    elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                        aeskey_start = quote_content.find('cdnthumbaeskey="') + 16
                        aeskey_end = quote_content.find('"', aeskey_start)
                        aeskey = quote_content[aeskey_start:aeskey_end]

                        cdnmidimgurl_start = quote_content.find('cdnthumburl="') + 13
                        cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                        cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]

                    else:
                        if self.natural_response:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                        else:
                            await bot.send_at_message(wxid, "❌ 无法从引用消息中提取图片信息", [user_wxid])
                        return
                else:
                    # 提取aeskey和cdnmidimgurl
                    aeskey = img_node.get('aeskey')
                    cdnmidimgurl = img_node.get('cdnmidimgurl')

                    if not aeskey or not cdnmidimgurl:
                        # 尝试提取缩略图信息
                        aeskey = img_node.get('cdnthumbaeskey')
                        cdnmidimgurl = img_node.get('cdnthumburl')

                if not aeskey or not cdnmidimgurl:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, "❌ 无法提取图片下载参数", [user_wxid])
                    return

                # 使用API下载图片
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                if not image_base64:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, "❌ 下载图片失败", [user_wxid])
                    return

                # 保存图片到临时文件
                temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
                image_data = base64.b64decode(image_base64)
                with open(temp_file, "wb") as f:
                    f.write(image_data)

                logger.info(f"[{self.plugin_name}] 成功下载引用的图片并保存到: {temp_file}")

                # 执行图生图流程
                await self._image_generation_flow_without_notification(bot, wxid, user_wxid, prompt, str(temp_file), ratio_config, style_value)

                # 处理完成后清理临时文件（延迟10秒）
                cleanup_file(str(temp_file), delay_seconds=10)

            except ET.ParseError as e:
                logger.error(f"[{self.plugin_name}] 解析XML失败: {str(e)}")
                logger.error(f"[{self.plugin_name}] XML内容: {quote_content[:200]}...")
                await bot.send_at_message(
                    wxid,
                    "❌ 解析图片信息失败",
                    [user_wxid]
                )
                return

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片引用失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理图片引用失败: {str(e)}",
                [user_wxid]
            )

    # 识图功能已移植到DoubaoImageRecognition插件
    async def _recognition_flow_disabled(self, bot, wxid, user_wxid, prompt, image_path):
        logger.info(f"[{self.plugin_name}] 识图功能已移植到DoubaoImageRecognition插件")
        await bot.send_at_message(wxid, "识图功能已移植到DoubaoImageRecognition插件，请使用新插件", [user_wxid])
        return

    # 识图功能已移植到DoubaoImageRecognition插件
    # async def _recognition_flow(self, bot, wxid, user_wxid, prompt, image_path):

    # 识图功能已移植到DoubaoImageRecognition插件
    # async def _recognition_flow_without_notification(self, bot, wxid, user_wxid, prompt, image_path):

    async def _image_generation_flow_without_notification(self, bot, wxid, user_wxid, prompt, image_path, ratio_config=None, style_value=None):
        try:
            # 使用默认比例配置
            if ratio_config is None:
                ratio_config = self.aspect_ratios["3:4"]

            logger.info(f"[{self.plugin_name}] 开始豆包AI处理流程(无通知)，用户: {user_wxid}, 提示词: {prompt}, 比例: {ratio_config['width']}x{ratio_config['height']}")

            # 步骤1: 验证图片文件
            logger.info(f"[{self.plugin_name}] 步骤1: 验证图片文件...")
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")

            with open(image_path, "rb") as f:
                image_data = f.read()
            logger.info(f"[{self.plugin_name}] 图片验证成功，大小: {len(image_data)/1024:.1f}KB")

            # 步骤2: 调用豆包AI图生图接口
            logger.info(f"[{self.plugin_name}] 步骤2: 调用豆包AI接口...")
            text_image_pairs = await self._call_doubao_image_to_image(prompt, image_path, ratio_config, style_value)

            if text_image_pairs:
                logger.info(f"[{self.plugin_name}] 豆包AI处理完成，生成了{len(text_image_pairs)}个文本-图片配对")

                # 逐个发送文本-图片配对（只发送有图片的配对）
                valid_pairs = [pair for pair in text_image_pairs if pair['image_url']]
                logger.info(f"[{self.plugin_name}] 有效配对数量: {len(valid_pairs)}/{len(text_image_pairs)}")

                for i, pair in enumerate(valid_pairs):
                    logger.info(f"[{self.plugin_name}] 处理配对 {i+1}: 文本='{pair['text'][:50]}...', 图片={'有' if pair['image_url'] else '无'}")

                    # 先发送文本
                    if pair['text']:
                        logger.info(f"[{self.plugin_name}] 发送文本 {i+1}: '{pair['text']}'")
                        await bot.send_text_message(wxid, pair['text'])
                        await asyncio.sleep(0.5)  # 短暂延迟确保消息顺序

                    # 再发送图片
                    if pair['image_url']:
                        logger.info(f"[{self.plugin_name}] 发送图片 {i+1}: {pair['image_url']}")
                        result = {
                            "url": pair['image_url'],
                            "width": ratio_config["width"],
                            "height": ratio_config["height"],
                            "format": "jpg",
                            "text_response": None  # 文本已单独发送
                        }
                        await self._send_results(bot, wxid, user_wxid, [result])
                        await asyncio.sleep(0.5)  # 短暂延迟确保消息顺序
            else:
                logger.warning(f"[{self.plugin_name}] 豆包AI处理失败")
                await bot.send_at_message(
                    wxid,
                    "豆包AI处理失败，等会再试试吧",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理失败: {str(e)}",
                [user_wxid]
            )

    async def _image_analysis_fallback(self, bot, wxid, user_wxid, prompt, image_path):
        try:
            logger.info(f"[{self.plugin_name}] 使用替代方案进行图片分析")

            # 直接读取图片并转为base64
            with open(image_path, "rb") as f:
                image_data = f.read()

            # 尝试获取图片信息
            try:
                from PIL import Image
                with Image.open(image_path) as img:
                    width, height = img.size
                    format_name = img.format
            except ImportError:
                width = height = format_name = "未知(需要Pillow库)", "未知(需要Pillow库)", "未知(需要Pillow库)"
            except Exception:
                width = height = format_name = "解析失败", "解析失败", "解析失败"

            logger.info(f"[{self.plugin_name}] 图片分析结果: 大小{len(image_data)/1024:.1f}KB, 尺寸{width}x{height}, 格式{format_name}")

            # 向用户发送简化的报告，不重复发送提示
            await bot.send_at_message(
                wxid,
                f"""❌ 上传服务暂时不可用，图片分析结果:

📊 图片信息: {width}x{height} {format_name}
📁 文件大小: {len(image_data)/1024:.1f} KB
💡 提示词: {prompt}

等会再试试或找管理员""",
                [user_wxid]
            )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 替代测试失败: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 图片分析失败: {str(e)}",
                [user_wxid]
            )

    async def _image_generation_flow(self, bot, wxid, user_wxid, prompt, image_path, ratio_config=None, style_value=None):
        try:
            # 使用默认比例配置
            if ratio_config is None:
                ratio_config = self.aspect_ratios["3:4"]

            # 不再需要预先确认，等待豆包AI的文字响应

            logger.info(f"[{self.plugin_name}] 开始豆包AI处理流程，用户: {user_wxid}, 提示词: {prompt}, 比例: {ratio_config['width']}x{ratio_config['height']}")

            # 步骤1: 验证图片文件
            logger.info(f"[{self.plugin_name}] 步骤1: 验证图片文件...")
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")

            with open(image_path, "rb") as f:
                image_data = f.read()
            logger.info(f"[{self.plugin_name}] 图片验证成功，大小: {len(image_data)/1024:.1f}KB")

            # 步骤2: 调用豆包AI图生图接口
            logger.info(f"[{self.plugin_name}] 步骤2: 调用豆包AI接口...")
            text_image_pairs = await self._call_doubao_image_to_image(prompt, image_path, ratio_config, style_value)

            if text_image_pairs:
                logger.info(f"[{self.plugin_name}] 豆包AI处理完成，生成了{len(text_image_pairs)}个文本-图片配对")

                # 逐个发送文本-图片配对（只发送有图片的配对）
                valid_pairs = [pair for pair in text_image_pairs if pair['image_url']]
                logger.info(f"[{self.plugin_name}] 有效配对数量: {len(valid_pairs)}/{len(text_image_pairs)}")

                for i, pair in enumerate(valid_pairs):
                    logger.info(f"[{self.plugin_name}] 处理配对 {i+1}: 文本='{pair['text'][:50]}...', 图片={'有' if pair['image_url'] else '无'}")

                    # 先发送文本
                    if pair['text']:
                        logger.info(f"[{self.plugin_name}] 发送文本 {i+1}: '{pair['text']}'")
                        await bot.send_text_message(wxid, pair['text'])
                        await asyncio.sleep(0.5)  # 短暂延迟确保消息顺序

                    # 再发送图片
                    if pair['image_url']:
                        logger.info(f"[{self.plugin_name}] 发送图片 {i+1}: {pair['image_url']}")
                        result = {
                            "url": pair['image_url'],
                            "width": ratio_config["width"],
                            "height": ratio_config["height"],
                            "format": "jpg",
                            "text_response": None  # 文本已单独发送
                        }
                        await self._send_results(bot, wxid, user_wxid, [result])
                        await asyncio.sleep(0.5)  # 短暂延迟确保消息顺序
            else:
                logger.warning(f"[{self.plugin_name}] 豆包AI处理失败")
                await bot.send_at_message(
                    wxid,
                    "豆包AI处理失败，等会再试试吧",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理失败: {str(e)}",
                [user_wxid]
            )

    # 识图功能已移植到DoubaoImageRecognition插件
    # async def _call_doubao_recognition(self, prompt, image_path):

    async def _call_doubao_image_to_image(self, prompt, image_path, ratio_config, style_value=None):
        """调用豆包AI图生图接口

        Args:
            prompt: 已经包含比例信息的完整提示词
            image_path: 图片路径
            ratio_config: 比例配置字典
            style_value: 可选的风格值
        """
        try:
            logger.info(f"[{self.plugin_name}] ========== 开始豆包AI图生图处理 ==========")
            logger.info(f"[{self.plugin_name}] 输入参数:")
            logger.info(f"[{self.plugin_name}]   - 提示词: '{prompt}'")
            logger.info(f"[{self.plugin_name}]   - 图片路径: {image_path}")
            logger.info(f"[{self.plugin_name}]   - 比例配置: {ratio_config['ratio_text']} ({ratio_config['width']}x{ratio_config['height']})")
            logger.info(f"[{self.plugin_name}]   - 风格值: {style_value}")

            # 检查Cookie配置
            if not self.cookies:
                logger.error(f"[{self.plugin_name}] ❌ 豆包AI Cookie未配置")
                return None

            logger.debug(f"[{self.plugin_name}] Cookie配置检查通过，长度: {len(self.cookies)}")

            # 验证图片文件
            if not os.path.exists(image_path):
                logger.error(f"[{self.plugin_name}] ❌ 图片文件不存在: {image_path}")
                return None

            file_size = os.path.getsize(image_path)
            logger.info(f"[{self.plugin_name}] 图片文件验证通过，大小: {file_size/1024:.1f}KB")

            # 创建豆包AI生成器实例
            logger.debug(f"[{self.plugin_name}] 创建豆包AI生成器实例...")
            generator = DoubaoImageGenerator(self.cookies)

            # 调用完整的图片处理流程
            logger.info(f"[{self.plugin_name}] 开始调用豆包AI处理流程...")
            start_time = time.time()

            text_image_pairs, text_response = await generator.process_image(
                image_path,
                prompt,  # 传递包含比例信息的完整提示词
                style_value  # 传递风格参数
            )

            processing_time = time.time() - start_time
            logger.info(f"[{self.plugin_name}] 豆包AI处理完成，耗时: {processing_time:.1f}秒")

            # 图生图完成后清理临时文件（延迟10秒，确保处理完成）
            if image_path.startswith(str(self.temp_dir)):
                logger.debug(f"[{self.plugin_name}] 标记临时文件清理: {image_path}")
                cleanup_file(image_path, delay_seconds=10)

            if text_image_pairs and len(text_image_pairs) > 0:
                logger.info(f"[{self.plugin_name}] ✅ 豆包AI处理成功，生成了 {len(text_image_pairs)} 个文本-图片配对")
                logger.info(f"[{self.plugin_name}] 完整文本响应: '{text_response}'" if text_response else f"[{self.plugin_name}] 无文本响应")

                # 返回文本-图片配对，让调用方处理发送
                logger.info(f"[{self.plugin_name}] ========== 豆包AI图生图处理完成 ==========")
                return text_image_pairs
            else:
                logger.error(f"[{self.plugin_name}] ❌ 豆包AI处理失败，未获得生成配对")
                logger.info(f"[{self.plugin_name}] ========== 豆包AI图生图处理失败 ==========")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] ❌ 调用豆包AI异常: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            logger.info(f"[{self.plugin_name}] ========== 豆包AI图生图处理异常结束 ==========")
            return None

    async def _send_results(self, bot, wxid, user_wxid, results, prompt=None):
        """发送生成结果

        Args:
            bot: 微信机器人API客户端
            wxid: 聊天ID
            user_wxid: 用户微信ID
            results: 生成的图片结果列表
            prompt: 原始提示词
        """
        try:
            logger.info(f"[{self.plugin_name}] ========== 开始发送生成结果 ==========")
            logger.info(f"[{self.plugin_name}] 待发送图片数量: {len(results)}")

            # 下载并发送每张图片，不发送提示信息
            success_count = 0
            for i, image_info in enumerate(results):
                logger.info(f"[{self.plugin_name}] 处理第 {i+1}/{len(results)} 张图片...")

                image_url = image_info.get("url")
                if not image_url:
                    logger.warning(f"[{self.plugin_name}] 第 {i+1} 张图片URL为空，跳过")
                    continue

                logger.debug(f"[{self.plugin_name}] 图片URL: {image_url}")

                try:
                    # 下载图片
                    logger.debug(f"[{self.plugin_name}] 开始下载第 {i+1} 张图片...")
                    download_start = time.time()

                    async with httpx.AsyncClient(timeout=30) as client:
                        response = await client.get(image_url)
                        response.raise_for_status()

                        # 获取图片二进制数据
                        image_data = response.content
                        download_time = time.time() - download_start

                        logger.info(f"[{self.plugin_name}] 第 {i+1} 张图片下载完成，耗时: {download_time:.1f}秒")

                        # 验证图片大小
                        if len(image_data) < 1024:  # 小于1KB可能有问题
                            raise ValueError(f"下载的图片数据过小: {len(image_data)}字节")

                        logger.info(f"[{self.plugin_name}] 图片数据验证通过，大小: {len(image_data)/1024:.1f}KB")

                        # 使用send_image_message直接发送图片数据而不是文件路径
                        logger.debug(f"[{self.plugin_name}] 开始发送第 {i+1} 张图片...")
                        send_start = time.time()

                        result = await bot.send_image_message(wxid, image_data)
                        send_time = time.time() - send_start

                        logger.debug(f"[{self.plugin_name}] 发送结果: {result}")

                        if result and result[2] != 0:  # new_msg_id不为0表示发送成功
                            logger.info(f"[{self.plugin_name}] ✅ 第 {i+1}/{len(results)} 张图片发送成功，耗时: {send_time:.1f}秒")
                            success_count += 1
                        else:
                            logger.warning(f"[{self.plugin_name}] ⚠️ 第 {i+1}/{len(results)} 张图片主方案发送失败，尝试备用方案...")

                            # 备用方案：保存到临时文件并使用send_file_message
                            temp_file = self.temp_dir / f"result_{int(time.time())}_{i}.jpg"
                            with open(temp_file, "wb") as f:
                                f.write(image_data)

                            logger.debug(f"[{self.plugin_name}] 临时文件保存: {temp_file}")

                            backup_start = time.time()
                            file_result = await bot.send_file_message(wxid, str(temp_file))
                            backup_time = time.time() - backup_start

                            logger.debug(f"[{self.plugin_name}] 备用方案结果: {file_result}")

                            if file_result and file_result[2] != 0:
                                logger.info(f"[{self.plugin_name}] ✅ 第 {i+1}/{len(results)} 张图片备用方案发送成功，耗时: {backup_time:.1f}秒")
                                success_count += 1
                            else:
                                logger.error(f"[{self.plugin_name}] ❌ 第 {i+1}/{len(results)} 张图片备用方案也发送失败")

                            # 清理临时文件
                            logger.debug(f"[{self.plugin_name}] 标记临时文件清理: {temp_file}")
                            cleanup_file(str(temp_file), delay_seconds=5)

                        # 短暂延迟，避免发送过快
                        logger.debug(f"[{self.plugin_name}] 等待1.5秒后处理下一张图片...")
                        await asyncio.sleep(1.5)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] ❌ 处理第 {i+1} 张图片时出错: {str(e)}")
                    import traceback
                    logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
                    continue

            logger.info(f"[{self.plugin_name}] ========== 图片发送完成 ==========")
            logger.info(f"[{self.plugin_name}] 发送结果: 成功 {success_count}/{len(results)} 张")

            # 如果所有图片都发送失败，才通知用户
            if success_count == 0:
                logger.error(f"[{self.plugin_name}] 所有图片发送失败，通知用户")
                await bot.send_at_message(
                    wxid,
                    "图片都发不出去，网络有问题",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] ❌ 发送结果异常: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 发送结果失败: {str(e)}",
                [user_wxid]
            )

    async def download_image(self, bot, aeskey, cdnmidimgurl):
        """下载图片并返回base64编码字符串

        Args:
            bot (WechatAPIClient): 微信API客户端实例
            aeskey (str): 图片的AES密钥
            cdnmidimgurl (str): 图片的CDN URL

        Returns:
            str: 图片的base64编码字符串，失败则返回None
        """
        try:
            # 记录参数信息，帮助调试
            logger.info(f"[{self.plugin_name}] 尝试下载图片，参数: aeskey={aeskey}, cdnmidimgurl={cdnmidimgurl}")

            # 检查参数格式
            if not aeskey:
                logger.error(f"[{self.plugin_name}] aeskey为空")
                return None

            if not cdnmidimgurl:
                logger.error(f"[{self.plugin_name}] cdnmidimgurl为空")
                return None

            # 使用API下载图片
            try:
                # 直接调用API，不捕获异常，让异常向上传播
                logger.info(f"[{self.plugin_name}] 调用bot.download_image方法")
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                logger.info(f"[{self.plugin_name}] bot.download_image方法调用完成")

                # 检查返回值
                if not image_base64:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: API返回为空")
                    return None

                logger.info(f"[{self.plugin_name}] 图片下载成功，数据长度: {len(image_base64)}")
                return image_base64
            except Exception as api_error:
                logger.error(f"[{self.plugin_name}] 调用API下载图片时出错: {str(api_error)}")
                import traceback
                logger.error(f"[{self.plugin_name}] API异常详情: {traceback.format_exc()}")
                # 重新抛出异常，让调用者处理
                raise
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载图片异常: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            # 重新抛出异常，让调用者处理
            raise

    async def download_and_save_image(self, bot: WechatAPIClient, aeskey: str, cdnurl: str,
                                    msg_id=None, is_thumb=False):
        """下载并保存图片，返回base64编码和临时文件路径

        Args:
            bot (WechatAPIClient): 微信API客户端实例
            aeskey (str): 图片的AES密钥
            cdnurl (str): 图片的CDN URL (可以是cdnmidimgurl或cdnthumburl)
            msg_id (str, optional): 消息ID，用于生成文件名。如果为None，则使用时间戳
            is_thumb (bool, optional): 是否将参数视为缩略图参数

        Returns:
            Tuple[str, str]: (图片的base64编码字符串, 图片的本地路径)，如果失败则返回 (None, None)
        """
        try:
            # 使用 WechatAPIClient 的 download_image 方法下载图片
            if is_thumb:
                logger.info(f"[{self.plugin_name}] 使用缩略图参数下载图片: cdnthumbaeskey={aeskey}, cdnthumburl={cdnurl}")

                # 尝试使用构造的HTTP请求下载缩略图
                try:
                    # 构造请求参数
                    json_param = {"Wxid": bot.wxid, "AesKey": aeskey, "Cdnthumburl": cdnurl}

                    # 获取API服务器地址和端口
                    api_url = f'http://{bot.ip}:{bot.port}/CdnDownloadThumbImg'

                    # 使用httpx发送请求
                    async with httpx.AsyncClient() as client:
                        logger.info(f"[{self.plugin_name}] 发送HTTP请求到: {api_url}")
                        response = await client.post(api_url, json=json_param)

                        # 检查响应状态码
                        if response.status_code != 200:
                            logger.error(f"[{self.plugin_name}] HTTP请求失败: 状态码 {response.status_code}")
                            return None, None

                        # 解析响应JSON
                        json_resp = response.json()

                        # 检查响应是否成功
                        if not json_resp.get("Success"):
                            logger.error(f"[{self.plugin_name}] API返回失败: {json_resp}")
                            return None, None

                        # 获取图片数据
                        image_base64 = json_resp.get("Data")

                        if not image_base64:
                            logger.error(f"[{self.plugin_name}] 下载图片失败: API返回数据为空")
                            return None, None

                        logger.info(f"[{self.plugin_name}] 图片下载成功，数据长度: {len(image_base64)}")
                except Exception as http_error:
                    logger.error(f"[{self.plugin_name}] HTTP请求异常: {str(http_error)}")
                    import traceback
                    logger.error(f"[{self.plugin_name}] HTTP异常详情: {traceback.format_exc()}")
                    return None, None
            else:
                logger.info(f"[{self.plugin_name}] 使用 download_image 方法下载图片: aeskey={aeskey}, cdnurl={cdnurl}")

                # 直接调用 download_image 方法
                image_base64 = await bot.download_image(aeskey, cdnurl)

                if not image_base64:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: API返回为空")
                    return None, None

                logger.info(f"[{self.plugin_name}] 图片下载成功，数据长度: {len(image_base64)}")

            # 确保临时目录存在
            os.makedirs(self.temp_dir, exist_ok=True)

            # 生成文件名
            if not msg_id:
                msg_id = str(int(time.time()))

            # 生成文件路径和文件名
            file_name = f"{msg_id}.jpg"
            file_path = os.path.join(self.temp_dir, file_name)

            # 保存图片文件
            try:
                # 解码base64数据
                image_data = base64.b64decode(image_base64)
                # 写入文件
                with open(file_path, "wb") as f:
                    f.write(image_data)
                logger.info(f"[{self.plugin_name}] 图片已保存到: {file_path}")
                return image_base64, file_path
            except Exception as save_error:
                logger.error(f"[{self.plugin_name}] 保存图片失败: {str(save_error)}")
                return image_base64, None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载并保存图片异常: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            return None, None

    # 豆包AI不需要AWS4签名方法

    # 豆包AI不需要上传Token相关方法

    # 豆包AI不需要申请上传地址相关方法

    # 豆包AI不需要上传图片相关方法


    # 豆包AI不需要即梦AI的任务构建相关方法

    # 豆包AI不需要即梦AI的任务提交相关方法

    # 豆包AI不需要即梦AI的复杂签名相关方法

    def _check_rate_limit(self, user_wxid):
        """检查用户请求频率限制

        Args:
            user_wxid: 用户微信ID

        Returns:
            bool: True表示已达到限制（应该拒绝请求），False表示未达到限制（可以处理请求）
        """
        current_time = time.time()

        logger.debug(f"[{self.plugin_name}] 检查用户限流状态: {user_wxid}")
        logger.debug(f"[{self.plugin_name}] 当前时间: {current_time}, 冷却时间: {self.cooldown}秒")

        # 如果是新用户或者已经过了冷却时间，重置计数
        if user_wxid not in self.user_last_request:
            logger.info(f"[{self.plugin_name}] 新用户 {user_wxid}，允许请求")
            self.user_last_request[user_wxid] = current_time
            self.user_request_count[user_wxid] = 1
            return False  # 未达到限制

        last_request_time = self.user_last_request.get(user_wxid, 0)
        time_since_last = current_time - last_request_time

        logger.debug(f"[{self.plugin_name}] 用户 {user_wxid} 上次请求时间: {last_request_time}")
        logger.debug(f"[{self.plugin_name}] 距离上次请求: {time_since_last:.1f}秒")

        if time_since_last > self.cooldown:
            logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 冷却时间已过({time_since_last:.1f}s > {self.cooldown}s)，重置计数，允许请求")
            self.user_last_request[user_wxid] = current_time
            self.user_request_count[user_wxid] = 1
            return False  # 未达到限制

        # 更新最后请求时间
        self.user_last_request[user_wxid] = current_time

        # 增加请求计数
        old_count = self.user_request_count.get(user_wxid, 0)
        self.user_request_count[user_wxid] = old_count + 1

        # 记录日志
        remaining_time = self.cooldown - time_since_last
        logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 触发限流")
        logger.info(f"[{self.plugin_name}] 冷却时间内第 {self.user_request_count[user_wxid]} 次请求，剩余冷却时间: {remaining_time:.1f}秒")

        # 如果在冷却时间内，返回True表示已达到限制
        return True

    # 豆包AI不需要即梦AI的结果等待相关方法

    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        usage_message = f"🎨 豆包AI图生图使用说明\n📝 命令格式: {self.command_format}\n💡 引用图片示例: 豆包 动漫风格转换 1:1\n📐 比例: 1:1/2:3/4:3/9:16/16:9\n🎨 风格: 动漫/油画/水墨画/人像摄影等\n\n⚠️ 识图功能已移至DoubaoImageRecognition插件"
        await bot.send_at_message(wxid, usage_message, [user_wxid])

