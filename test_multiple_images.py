#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多图片处理功能
"""

import asyncio
import json

async def test_multiple_images():
    """测试多图片处理功能"""
    
    # 模拟响应数据（基于实际响应内容）
    mock_response_lines = [
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":2074,\\"content\\":\\"{\\\\\\"creations\\\\\\":[{\\\\\\"type\\\\\\":1,\\\\\\"image\\\\\\":{\\\\\\"status\\\\\\":2,\\\\\\"image_ori\\\\\\":{\\\\\\"url\\\\\\":\\\\\\"https://example.com/image1.jpg\\\\\\"}}}]}\\",\\"id\\":\\"test1\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":10000,\\"content\\":\\"{\\\\\\"text\\\\\\":\\\\\\"第一张图片完成\\\\\\"}\\",\\"id\\":\\"test2\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":2074,\\"content\\":\\"{\\\\\\"creations\\\\\\":[{\\\\\\"type\\\\\\":1,\\\\\\"image\\\\\\":{\\\\\\"status\\\\\\":2,\\\\\\"image_ori\\\\\\":{\\\\\\"url\\\\\\":\\\\\\"https://example.com/image2.jpg\\\\\\"}}}]}\\",\\"id\\":\\"test3\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":10000,\\"content\\":\\"{\\\\\\"text\\\\\\":\\\\\\"第二张图片完成\\\\\\"}\\",\\"id\\":\\"test4\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":2074,\\"content\\":\\"{\\\\\\"creations\\\\\\":[{\\\\\\"type\\\\\\":1,\\\\\\"image\\\\\\":{\\\\\\"status\\\\\\":2,\\\\\\"image_ori\\\\\\":{\\\\\\"url\\\\\\":\\\\\\"https://example.com/image3.jpg\\\\\\"}}}]}\\",\\"id\\":\\"test5\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":10000,\\"content\\":\\"{\\\\\\"text\\\\\\":\\\\\\"第三张图片完成\\\\\\"}\\",\\"id\\":\\"test6\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":2074,\\"content\\":\\"{\\\\\\"creations\\\\\\":[{\\\\\\"type\\\\\\":1,\\\\\\"image\\\\\\":{\\\\\\"status\\\\\\":2,\\\\\\"image_ori\\\\\\":{\\\\\\"url\\\\\\":\\\\\\"https://example.com/image4.jpg\\\\\\"}}}]}\\",\\"id\\":\\"test7\\"}}","event_type":2001}',
        'data: {"event_data":"{\\"message\\":{\\"content_type\\":10000,\\"content\\":\\"{\\\\\\"text\\\\\\":\\\\\\"第四张图片完成\\\\\\"}\\",\\"id\\":\\"test8\\"}}","event_type":2001}',
        'data: [DONE]'
    ]
    
    print("测试多图片处理逻辑...")
    
    # 模拟处理逻辑
    generated_images = []
    text_response = ""
    
    for line in mock_response_lines:
        if not line.startswith('data: '):
            continue
            
        data_str = line[6:].strip()
        if not data_str or data_str == '[DONE]':
            continue
            
        try:
            event_data = json.loads(data_str)
            
            if 'event_data' in event_data and event_data.get('event_type') == 2001:
                inner_data = json.loads(event_data['event_data'])
                
                if 'message' in inner_data:
                    message = inner_data['message']
                    content_type = message.get('content_type')
                    
                    # 处理图片生成结果 (content_type 2074)
                    if content_type == 2074 and 'content' in message:
                        content = json.loads(message['content'])
                        
                        if 'creations' in content:
                            for creation in content['creations']:
                                if creation.get('type') == 1 and 'image' in creation:
                                    image_info = creation['image']
                                    status = image_info.get('status')
                                    
                                    if status == 2:  # 生成成功
                                        url_obj = image_info.get('image_ori')
                                        url = url_obj.get('url') if isinstance(url_obj, dict) else None
                                        if url:
                                            print(f"找到图片: {url}")
                                            generated_images.append(url)
                    
                    # 处理文本响应 (content_type 10000)
                    elif content_type == 10000:
                        content = json.loads(message['content'])
                        if 'text' in content and content['text']:
                            text_response += content['text']
                            
        except Exception as e:
            print(f"处理行时出错: {e}")
            continue
    
    print(f"\n测试结果:")
    print(f"收集到的图片数量: {len(generated_images)}")
    print(f"图片URLs:")
    for i, url in enumerate(generated_images):
        print(f"  {i+1}. {url}")
    print(f"文本响应: '{text_response}'")
    
    # 验证结果
    expected_count = 4
    if len(generated_images) == expected_count:
        print(f"\n✅ 测试通过！成功收集到 {expected_count} 张图片")
        return True
    else:
        print(f"\n❌ 测试失败！期望 {expected_count} 张图片，实际收集到 {len(generated_images)} 张")
        return False

if __name__ == "__main__":
    asyncio.run(test_multiple_images())
