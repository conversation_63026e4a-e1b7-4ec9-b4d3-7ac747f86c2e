2025-08-01 08:54:02 | DEBUG | [DoubaoImageGenerator] 流式响应状态: 200
2025-08-01 08:54:02 | INFO | [<PERSON>ubao<PERSON>mageGenerator] 开始处理流式响应...
2025-08-01 08:54:02 | DEBUG | [<PERSON><PERSON><PERSON><PERSON>mageGenerator] 第1行事件数据: {'event_data': '{"message_id":"14414572051600386","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","message_index":1,"conversation_type":5}', 'event_id': '0', 'event_type': 2002}
2025-08-01 08:54:04 | DEBUG | [<PERSON><PERSON><PERSON><PERSON><PERSON>Generator] 第3行事件数据: {'event_data': '{"proto_version":2,"message_id":"14414572051600386","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","message_index":1,"conversation_type":5}', 'event_id': '1', 'event_type': 2011}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第5行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"好\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '2', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"好"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第7行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '3', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第9行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '4', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第11行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"我\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '5', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"我"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第13行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '6', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第15行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '7', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第17行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '8', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第19行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"修改\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '9', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"修改"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第21行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第一张\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '10', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第一张"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第23行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"图片\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '11', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"图片"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第25行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"中\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '12', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"中"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第27行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"人物\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '13', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"人物"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第29行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '14', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第31行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '15', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第33行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '16', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第35行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"同时\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '17', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"同时"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 第37行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '18', 'event_type': 2001}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:06 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第39行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"图片\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '19', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"图片"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第41行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"比例\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '20', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"比例"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第43行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"调整\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '21', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"调整"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第45行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '22', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第47行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"2\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '23', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"2"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第49行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\":\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '24', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":":"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第51行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"3\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '25', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"3"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 第53行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"0be68f71-a20e-40b3-8401-d5128b1959b6"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '26', 'event_type': 2001}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': '0be68f71-a20e-40b3-8401-d5128b1959b6'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:07 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第55行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"2e7a2989-a9c9-4b86-bbaa-50947bd22845"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '27', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '2e7a2989-a9c9-4b86-bbaa-50947bd22845'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 08:54:09 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 08:54:09 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第57行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"已\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '28', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"已"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第59行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"完成\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '29', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"完成"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第61行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第一个\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '30', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第一个"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第63行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '31', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第65行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '32', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第67行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"修改\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '33', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"修改"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第69行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '34', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第71行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"接下来\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '35', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"接下来"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第73行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '36', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第75行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '37', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第77行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"生成\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '38', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"生成"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第79行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第二个\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '39', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第二个"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第81行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '40', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第83行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '41', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 第85行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '42', 'event_type': 2001}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': 'a4f2c5cc-3d2f-4a2b-bcbf-2a91dcf619f1'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:09 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第87行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"2f8499a4-583e-4547-ae24-20ad5a84d24d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '43', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '2f8499a4-583e-4547-ae24-20ad5a84d24d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 08:54:12 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 08:54:12 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第89行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"已\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '44', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"已"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第91行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"完成\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '45', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"完成"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第93行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第二个\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '46', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第二个"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第95行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '47', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第97行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '48', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第99行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"修改\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '49', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"修改"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第101行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '50', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第103行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"接下来\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '51', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"接下来"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第105行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '52', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第107行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '53', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第109行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"生成\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '54', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"生成"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第111行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第三个\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '55', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第三个"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第113行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '56', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第115行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '57', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 第117行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"fab03661-9992-40ed-9e4a-0619d476dc6d"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '58', 'event_type': 2001}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': 'fab03661-9992-40ed-9e4a-0619d476dc6d'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:12 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第119行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"6d33e56b-012e-4c83-96a9-b881d43cf4b2"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '59', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '6d33e56b-012e-4c83-96a9-b881d43cf4b2'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 08:54:15 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 08:54:15 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第121行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"已\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '60', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"已"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第123行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"完成\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '61', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"完成"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第125行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第三个\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '62', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第三个"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第127行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '63', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第129行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '64', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第131行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"修改\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '65', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"修改"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第133行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '66', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第135行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"接下来\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '67', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"接下来"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第137行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '68', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第139行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '69', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第141行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"生成\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '70', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"生成"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第143行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"第四个\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '71', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"第四个"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第145行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '72', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个不同
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第147行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '73', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个不同姿势
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 第149行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"571e0b4f-7208-4b1b-a7ec-a384e94ba88f"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '74', 'event_type': 2001}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': '571e0b4f-7208-4b1b-a7ec-a384e94ba88f'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 08:54:15 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个不同姿势。
2025-08-01 08:54:17 | DEBUG | [DoubaoImageGenerator] 第151行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"184f5668-0cfb-415a-b52e-cf716f059e7a"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '75', 'event_type': 2001}
2025-08-01 08:54:17 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '184f5668-0cfb-415a-b52e-cf716f059e7a'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:17 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 08:54:17 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 08:54:17 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 08:54:17 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 08:54:17 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 08:54:24 | DEBUG | [DoubaoImageGenerator] 第153行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"key\\":\\"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96\\",\\"image_thumb\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=zaqO6P1m1VLlo%2F7y0vfsxX9%2FHEQ%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"image_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=%2BhhDpGgE5WzFWT5QJ5gB0aa9ReA%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_raw\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_thumb_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=3QOmIo6NYGyz9GmEj0QxcmkNyKg%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"description\\":\\"图片\\",\\"image_thumb_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=StFhSQ8rJttW%2F9pdKIjN9LU%2FAJ0%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=FV9ik0NjrbDVjl0COhLsX45O6Mg%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"image_thumb_ori_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=GRe0weitdtpFEZVufL29T4V0%2Bp0%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=Ex6SsPJLm9xr3KEd6el66imNU8o%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"status\\":2,\\"gen_params\\":{\\"prompt\\":\\"人物姿势调整为侧身倚靠在扶梯扶手上，左手自然垂下握住手提包，右手轻搭在扶手上，头部微侧看向镜头\\",\\"neg_prompt\\":\\"画面模糊，低质量\\",\\"img_uri\\":\\"tos-cn-i-a9rns2rl98/6594f894bbcd4576aa84ff22ec6c0560.jpg\\"},\\"preview_img\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069369668\\\\u0026x-signature=QT9uhxN0NTzXbLCnprnNHyzd0Bo%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536}}}]}","reset":true,"id":"2e7a2989-a9c9-4b86-bbaa-50947bd22845"},"message_id":"14414572051600642","local_message_id":"5b82e41c-cfad-4f78-b61b-ee1e3c5c52af","conversation_id":"14467706649802498","local_conversation_id":"local_1754009642021293","section_id":"14467706649802754","reply_id":"14414572051600386","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '76', 'event_type': 2001}
2025-08-01 08:54:24 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"key":"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96","image_thumb":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=zaqO6P1m1VLlo%2F7y0vfsxX9%2FHEQ%3D","format":"jpeg","width":386,"height":580},"image_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=%2BhhDpGgE5WzFWT5QJ5gB0aa9ReA%3D","format":"png","width":1024,"height":1536},"image_raw":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D","format":"png","width":1024,"height":1536},"image_thumb_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=3QOmIo6NYGyz9GmEj0QxcmkNyKg%3D","format":"jpeg","width":386,"height":580},"description":"图片","image_thumb_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=StFhSQ8rJttW%2F9pdKIjN9LU%2FAJ0%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=FV9ik0NjrbDVjl0COhLsX45O6Mg%3D","format":"webp","width":386,"height":580}},"image_thumb_ori_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=GRe0weitdtpFEZVufL29T4V0%2Bp0%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=Ex6SsPJLm9xr3KEd6el66imNU8o%3D","format":"webp","width":386,"height":580}},"status":2,"gen_params":{"prompt":"人物姿势调整为侧身倚靠在扶梯扶手上，左手自然垂下握住手提包，右手轻搭在扶手上，头部微侧看向镜头","neg_prompt":"画面模糊，低质量","img_uri":"tos-cn-i-a9rns2rl98/6594f894bbcd4576aa84ff22ec6c0560.jpg"},"preview_img":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069369668\\u0026x-signature=QT9uhxN0NTzXbLCnprnNHyzd0Bo%3D","format":"png","width":1024,"height":1536}}}]}', 'reset': True, 'id': '2e7a2989-a9c9-4b86-bbaa-50947bd22845'}, 'message_id': '14414572051600642', 'local_message_id': '5b82e41c-cfad-4f78-b61b-ee1e3c5c52af', 'conversation_id': '14467706649802498', 'local_conversation_id': 'local_1754009642021293', 'section_id': '14467706649802754', 'reply_id': '14414572051600386', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 08:54:24 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 08:54:24 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=zaqO6P1m1VLlo%2F7y0vfsxX9%2FHEQ%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=%2BhhDpGgE5WzFWT5QJ5gB0aa9ReA%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=3QOmIo6NYGyz9GmEj0QxcmkNyKg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=StFhSQ8rJttW%2F9pdKIjN9LU%2FAJ0%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=FV9ik0NjrbDVjl0COhLsX45O6Mg%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=GRe0weitdtpFEZVufL29T4V0%2Bp0%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=Ex6SsPJLm9xr3KEd6el66imNU8o%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身倚靠在扶梯扶手上，左手自然垂下握住手提包，右手轻搭在扶手上，头部微侧看向镜头', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/6594f894bbcd4576aa84ff22ec6c0560.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=QT9uhxN0NTzXbLCnprnNHyzd0Bo%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}]}
2025-08-01 08:54:24 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 08:54:24 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=zaqO6P1m1VLlo%2F7y0vfsxX9%2FHEQ%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=%2BhhDpGgE5WzFWT5QJ5gB0aa9ReA%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=3QOmIo6NYGyz9GmEj0QxcmkNyKg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=StFhSQ8rJttW%2F9pdKIjN9LU%2FAJ0%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=FV9ik0NjrbDVjl0COhLsX45O6Mg%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=GRe0weitdtpFEZVufL29T4V0%2Bp0%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=Ex6SsPJLm9xr3KEd6el66imNU8o%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身倚靠在扶梯扶手上，左手自然垂下握住手提包，右手轻搭在扶手上，头部微侧看向镜头', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/6594f894bbcd4576aa84ff22ec6c0560.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=QT9uhxN0NTzXbLCnprnNHyzd0Bo%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}
2025-08-01 08:54:24 | INFO | [DoubaoImageGenerator] 图片状态: 2
2025-08-01 08:54:24 | INFO | [DoubaoImageGenerator] 图片生成成功，URL类型: image_raw, URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D
2025-08-01 08:54:24 | INFO | [DoubaoImageGenerator] 图片处理流程完成，生成URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 豆包AI处理完成，耗时: 24.2秒
2025-08-01 08:54:24 | DEBUG | [DouBaoImageToImage] 标记临时文件清理: temp\doubao_image_to_image\quoted_image_1754009640.jpg
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] ✅ 豆包AI处理成功
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 生成图片URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 文本响应: '好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个不同姿势。'
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] ========== 豆包AI图生图处理完成 ==========
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 豆包AI处理完成，生成了1张图片
2025-08-01 08:54:24 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我将为你修改第一张图片中人物的姿势，同时将图片比例调整为2:3。已完成第一个姿势的修改，接下来为你生成第二个不同姿势。已完成第二个姿势的修改，接下来为你生成第三个不同姿势。已完成第三个姿势的修改，接下来为你生成第四个不同姿势。
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] ========== 开始发送生成结果 ==========
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 待发送图片数量: 1
2025-08-01 08:54:24 | INFO | [DouBaoImageToImage] 处理第 1/1 张图片...
2025-08-01 08:54:24 | DEBUG | [DouBaoImageToImage] 图片URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ab8aec5532424d68b626fccbaac7eb96~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069369668&x-signature=bPKt0dvkmAkekT835jPQfWUZOP0%3D
2025-08-01 08:54:24 | DEBUG | [DouBaoImageToImage] 开始下载第 1 张图片...
2025-08-01 08:54:27 | INFO | [DouBaoImageToImage] 第 1 张图片下载完成，耗时: 2.5秒
2025-08-01 08:54:27 | INFO | [DouBaoImageToImage] 图片数据验证通过，大小: 1865.0KB
2025-08-01 08:54:27 | DEBUG | [DouBaoImageToImage] 开始发送第 1 张图片...
2025-08-01 08:54:34 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1754009640.jpg
2025-08-01 08:54:36 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 08:54:36 | DEBUG | [DouBaoImageToImage] 发送结果: ('wxid_4usgcju5ey9q29_1754009667', 1754009681, 2909661889694170560)